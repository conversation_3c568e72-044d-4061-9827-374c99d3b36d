CREATE OR REPLACE FUNCTION public.tms_create_booking_v2(ins_id_ bigint, form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Response variables
    status boolean := false;
    message text := 'Internal_error';
    resp_data json;

    -- Input parameters
    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    srvc_type_id_ int;
    user_context_json json;

    -- Booking availability check
    availability_check json;
    booking_available boolean := false;
    required_man_minutes_ integer;
    capacity_id_ bigint;
    slot_day_ date;
    resource_id_ text;

    -- Booking creation variables
    order_label_ text;
    prev_form_data_ json;
    prev_booking_details_ json;
    existing_booking_ids_ int[];
    existing_booking_id_ bigint;
    booking_ids integer[] := '{}';
    start_booking_id_ bigint;
    booking_details_ json;
    manpower_ integer;

    -- Capacity variables
    available_capacity_ integer;
    total_cap_in_minutes_ integer;
    booked_cap_in_minutes_ integer;
    remaining_capacity_ integer;

    -- Across slots variables
    adjacent_slots_ids bigint[] := '{}';
    first_slot_capacity_id bigint;
    first_slot_day date;
    first_slot_start_time timestamp;
    current_capacity_id bigint;
    slot_record record;
    booking_qty_ integer;
    start_time_ timestamp;
    end_time_ timestamp;

    -- Booking status variables
    is_booked boolean := false;
    booking_message text := 'Booking failed';
    booking_update_json json;
    booking_slots_ jsonb;

    -- Bulk booking variables
    bulk_requests json[];
    bulk_request json;
    bulk_results json[] := '{}';
    bulk_success_count integer := 0;
    bulk_failure_count integer := 0;

BEGIN
    -- Extract basic parameters
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    ip_address_ := form_data_->>'ip_address';
    user_agent_ := form_data_->>'user_agent';
    srvc_type_id_ := (form_data_->>'srvc_type_id')::int;
    booking_details_ := form_data_->>'booking_data';

    -- Check if this is bulk booking (array of requests) or single booking
  
    IF ins_id_ IS NULL THEN
        RETURN json_build_object('status', false, 'message', 'ins_id is required for single booking');
    END IF;
 --   raise notice 'form else---------------1'; 
    -- Check booking availability
    availability_check := tms_hlpr_is_booking_available(form_data_);
    raise notice 'availability_check %',availability_check;
   --return '{}';
    IF NOT (availability_check->>'status')::boolean THEN
        -- Booking not available - send message to create_service_request for timeline
        booking_message := availability_check->>'message';
        is_booked := false;

        -- Prepare booking update JSON for timeline
        booking_update_json := json_build_object(
            'org_id', org_id_,
            'usr_id', usr_id_,
            'ip_address', ip_address_,
            'user_agent', user_agent_,
            'srvc_type_id', srvc_type_id_,
            'is_booked', is_booked,
            'booking_message', booking_message,
            'booking_data',booking_details_
        );

        -- Update service request with booking failure message
        PERFORM tms_create_service_request(booking_update_json, ins_id_::int);

        RETURN json_build_object(
            'status', false,
            'message', booking_message,
            'data', availability_check->'data',
            'is_booked', is_booked,
            'booking_message', booking_message
        );
    END IF;

    -- Extract booking details from availability check
    required_man_minutes_ := (availability_check->'data'->>'required_man_minutes')::integer;
    capacity_id_ := (availability_check->'data'->>'capacity_id')::bigint;
    slot_day_ := (availability_check->'data'->>'slot_day')::date;

    -- Extract manpower from form data (using the UUID key for manpower field)
    manpower_ := COALESCE((form_data_->>'6b858839-f154-4573-9e9b-4b01ebeb44a7')::integer, 1);

    -- Get service request details
    SELECT s.display_code, s.form_data
    INTO order_label_, prev_form_data_
    FROM cl_tx_srvc_req s
    WHERE s.db_id = ins_id_;

    prev_booking_details_ := COALESCE(prev_form_data_->'booking_data', '{}'::json);

    -- Get existing booking IDs for this service request
    SELECT array_agg(db_id)
    INTO existing_booking_ids_
    FROM cl_tx_bookings
    WHERE order_id = ins_id_ AND is_cancelled = false;

    -- Get capacity details
    SELECT available_capacity, total_cap_in_minutes, booked_cap_in_minutes, resource_id
    INTO available_capacity_, total_cap_in_minutes_, booked_cap_in_minutes_, resource_id_
    FROM cl_tx_capacity
    WHERE db_id = capacity_id_
      AND utilization < 1;

    IF NOT FOUND THEN
        RETURN json_build_object('status', false, 'message', 'Capacity not found or fully utilized');
    END IF;

    remaining_capacity_ := total_cap_in_minutes_ - booked_cap_in_minutes_;

    -- COMBINED BOOKING LOGIC STARTS HERE
 --   raise notice 'remaining_capacity_ %',remaining_capacity_;
    -- Step 1: Cancel existing bookings if any
    IF existing_booking_ids_ IS NOT NULL AND array_length(existing_booking_ids_, 1) > 0 THEN
        -- Mark existing bookings as cancelled
        UPDATE cl_tx_bookings
           SET is_cancelled = true,
               u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
         WHERE db_id = ANY(existing_booking_ids_);

        -- Release capacity from cancelled bookings
        UPDATE cl_tx_capacity
           SET  booked_cap_in_minutes = booked_cap_in_minutes - b.booked_qty,
               u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
          FROM cl_tx_bookings b
         WHERE cl_tx_capacity.db_id = b.capacity_id
           AND b.db_id = ANY(existing_booking_ids_);
    END IF;

    -- Step 2: Determine booking strategy and create bookings
    IF required_man_minutes_ <= remaining_capacity_ THEN
        -- WITHIN SLOT BOOKING
        INSERT INTO cl_tx_bookings (
            capacity_id, order_id, order_label, booked_qty, manpower, is_cancelled, c_meta, u_meta
        ) VALUES (
            capacity_id_, ins_id_, order_label_, required_man_minutes_, manpower_, false,
            row(ip_address_, user_agent_, now() at time zone 'utc'),
            row(ip_address_, user_agent_, now() at time zone 'utc')
        )
        RETURNING db_id INTO existing_booking_id_;
   --     raise notice 'existing_booking_id_ %',existing_booking_id_;
        -- Update capacity for the new booking
        UPDATE cl_tx_capacity
        SET booked_cap_in_minutes = booked_cap_in_minutes + required_man_minutes_,
            u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
        WHERE db_id = capacity_id_;

        booking_ids := array_append(booking_ids, existing_booking_id_::integer);
        start_booking_id_ := existing_booking_id_;
        is_booked := true;
        booking_message := 'Booking completed within single slot';

    ELSE
        -- ACROSS SLOTS BOOKING
        -- Find the first available slot
        SELECT db_id, usr_tmzone_day, start_time
          INTO first_slot_capacity_id, first_slot_day, first_slot_start_time
          FROM cl_tx_capacity
         WHERE resource_id = resource_id_
           AND available_capacity > 0
           AND usr_tmzone_day = COALESCE(slot_day_, (now() at time zone 'utc')::date)
           AND utilization < 1
         ORDER BY usr_tmzone_day ASC, start_time ASC
         LIMIT 1;

        IF first_slot_capacity_id IS NULL THEN
            RETURN json_build_object('status', false, 'message', 'No available slots found for across-slot booking');
        END IF;

        -- Build adjacent slots array starting with the first slot
        adjacent_slots_ids := array_append(adjacent_slots_ids, first_slot_capacity_id);

        -- Add next chronological slot
        SELECT db_id
          INTO current_capacity_id
          FROM cl_tx_capacity
         WHERE resource_id = resource_id_
           AND (
                usr_tmzone_day > first_slot_day
                OR (usr_tmzone_day = first_slot_day AND start_time::time > first_slot_start_time::time)
            )
           AND available_capacity > 0
           AND utilization < 1
           AND booked_cap_in_minutes < total_cap_in_minutes
         ORDER BY usr_tmzone_day ASC, start_time ASC
         LIMIT 1;

        IF current_capacity_id IS NOT NULL THEN
            adjacent_slots_ids := array_append(adjacent_slots_ids, current_capacity_id);
        END IF;

        -- Allocate booking across slots
        FOR slot_record IN
            SELECT db_id, total_cap_in_minutes, booked_cap_in_minutes, start_time
              FROM cl_tx_capacity
             WHERE db_id = ANY(adjacent_slots_ids)
               AND available_capacity > 0
               AND utilization < 1
               AND booked_cap_in_minutes < total_cap_in_minutes
             ORDER BY usr_tmzone_day ASC, start_time ASC
        LOOP
            EXIT WHEN required_man_minutes_ <= 0;

            remaining_capacity_ := slot_record.total_cap_in_minutes - slot_record.booked_cap_in_minutes;
            booking_qty_ := LEAST(required_man_minutes_, remaining_capacity_);

            INSERT INTO cl_tx_bookings (
                capacity_id, order_id, order_label, booked_qty, manpower, is_cancelled, c_meta, u_meta
            ) VALUES (
                slot_record.db_id, ins_id_, order_label_, booking_qty_, manpower_, false,
                row(ip_address_, user_agent_, now() at time zone 'utc'),
                row(ip_address_, user_agent_, now() at time zone 'utc')
            ) RETURNING db_id INTO existing_booking_id_;

            -- Collect booking IDs and remember the first one
            booking_ids := array_append(booking_ids, existing_booking_id_::integer);
            IF start_booking_id_ IS NULL THEN
                start_booking_id_ := existing_booking_id_;
            END IF;

            required_man_minutes_ := required_man_minutes_ - booking_qty_;

            UPDATE cl_tx_capacity
            SET booked_cap_in_minutes = booked_cap_in_minutes + booking_qty_,
                u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
            WHERE db_id = slot_record.db_id;
        END LOOP;

        -- Check if booking was successful
        IF required_man_minutes_ <= 0 THEN
            is_booked := true;
            booking_message := 'Booking completed across multiple slots';
        ELSE
            is_booked := false;
            booking_message := 'Insufficient capacity to complete booking across slots';
        END IF;
    END IF;

    -- Step 3: Update service request and create timeline
    IF is_booked THEN
        -- Set proper booking message based on existing bookings
        IF existing_booking_ids_ IS NOT NULL AND array_length(existing_booking_ids_, 1) > 0 THEN
            booking_message := 'Booking updated successfully - old booking(s) cancelled, new booking created';
        ELSE
            booking_message := 'Booking successful - Completed';
        END IF;

        -- Prepare booking update JSON
         user_context_json := json_build_object(
	        'org_id', org_id_,
	        'usr_id', usr_id_,
	        'ip_address', ip_address_,
	        'user_agent', user_agent_,
	        'srvc_type_id', srvc_type_id_
   		 );

	    booking_update_json := jsonb_set(user_context_json::jsonb, '{booking_id}', to_jsonb(booking_ids), true);
	    booking_update_json := jsonb_set(booking_update_json::jsonb, '{capacity_id}', to_jsonb(capacity_id_), true);
	  --  booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_data}', to_jsonb(booking_details_), true);
	    booking_update_json := jsonb_set(booking_update_json::jsonb, '{is_booked}', to_jsonb(is_booked), true);
	    booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_message}', to_jsonb(booking_message), true);
		booking_update_json := jsonb_set(booking_update_json::jsonb, '{start_booking_id}', to_jsonb(existing_booking_id_), true);
	    
		IF booking_details_ IS NULL THEN
		    -- Fetch the booking slots from cl_tx_capacity table using capacity_id_
		    SELECT start_time, end_time
		    INTO start_time_, end_time_
		    FROM cl_tx_capacity
		    WHERE db_id = capacity_id_ AND available_capacity > 0 AND utilization < 1
		    LIMIT 1;
		
		    -- If no slot is found, return error
--		    IF first_slot_start_time IS NULL THEN
--		        RETURN json_build_object('status', false, 'message', 'No available slots for the given capacity');
--		    END IF;
		     booking_slots_ := jsonb_build_object(
                                to_char(slot_day_, 'YYYY-MM-DD'),
                                jsonb_build_array(
                                    to_char(start_time_::timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'HH12:MIam') || ' - ' ||
                                    to_char(end_time_::timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'HH12:MIam')
                                )
                            );
              raise notice 'booking_slots_ %',booking_slots_;
             booking_details_ := jsonb_build_object(
                'selected_date', slot_day_,
                'capacity_id', capacity_id_,
                'booked_slots', booking_slots_
             );
             raise notice 'booking_slots_ %',booking_slots_;

            booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_data}', to_jsonb(booking_details_), true);
		  else
		     booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_data}', to_jsonb(booking_details_), true);
		  
		END IF;
--        if booking_details_ is not null then 
--        	booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_data}', to_jsonb(booking_details_), true);
--        end if;
        IF existing_booking_ids_ IS NOT NULL THEN
            booking_update_json := jsonb_set(booking_update_json::jsonb, '{cancelled_booking_ids}', to_jsonb(existing_booking_ids_), true);
        END IF;
		raise notice 'booking_update_json %',booking_update_json;
        -- Update service request
        PERFORM tms_create_service_request(booking_update_json, ins_id_::int);

        -- Sync total booked quantities
        PERFORM tms_hlpr_sync_booked_man_minutes_on_srvc_req(form_data_, ins_id_);

        status := true;
        message := booking_message;
        resp_data := json_build_object(
            'booking_ids', booking_ids,
            'start_booking_id', start_booking_id_,
            'capacity_id', capacity_id_,
            'required_man_minutes', (availability_check->'data'->>'required_man_minutes')::integer,
            'availability_check', availability_check->'data',
            'booking_details',booking_details_
        );
    ELSE
        -- Booking creation failed - send message to create_service_request for timeline
        booking_update_json := json_build_object(
            'org_id', org_id_,
            'usr_id', usr_id_,
            'ip_address', ip_address_,
            'user_agent', user_agent_,
            'srvc_type_id', srvc_type_id_,
            'is_booked', is_booked,
            'booking_message', booking_message
        );

        -- Update service request with booking failure message
        PERFORM tms_create_service_request(booking_update_json, ins_id_::int);

        status := false;
        message := booking_message;
        resp_data := json_build_object(
            'availability_check', availability_check->'data'
        );
    END IF;

 -- raise notice 'resp_data %',resp_data;
    RETURN json_build_object('status', status, 'message', message, 'data', resp_data);

 EXCEPTION WHEN others THEN
    RETURN json_build_object(
        'status', false,
        'message', 'Error in booking creation: ' || SQLERRM,
        'error_detail', SQLSTATE
    );
END;
$function$
;
